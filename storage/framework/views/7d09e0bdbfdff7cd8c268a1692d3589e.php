<?php $__env->startSection('title', 'Business Details'); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6" data-business-id="<?php echo e($business->id); ?>" data-user-id="<?php echo e(auth()->id()); ?>" data-can-manage="<?php echo e(auth()->check() && auth()->user()->hasPermission('manage_businesses') ? 'true' : 'false'); ?>">
    <div class="max-w-6xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white"><?php echo e($business->name); ?></h1>
            <div class="flex space-x-3">
                <a href="<?php echo e(route('business.index')); ?>"
                   class="bg-gray-500 dark:bg-gray-700 hover:bg-gray-600 dark:hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Back to Businesses
                </a>
                <?php if(auth()->user()->hasPermission('manage_businesses')): ?>
                    <a href="<?php echo e(route('business.edit', $business)); ?>"
                       class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Business
                    </a>
                <?php endif; ?>
            </div>
        </div>

        <!-- Tab Navigation -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700">
            <div class="border-b border-gray-200 dark:border-gray-700">
                <nav class="flex sm:space-x-8 px-6" aria-label="Tabs" role="tablist">
                    <button onclick="switchTab('business-info')"
                            id="business-info-tab"
                            class="tab-button border-blue-500 text-blue-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                            aria-selected="true" role="tab">
                        <i class="fas fa-building mr-2"></i>
                        Business Info
                    </button>
                    <button onclick="switchTab('contacts')"
                            id="contacts-tab"
                            class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                            aria-selected="false" role="tab">
                        <i class="fas fa-users mr-2"></i>
                        Contacts (<?php echo e($business->contacts->count()); ?>)
                    </button>
                    <button onclick="switchTab('documents')"
                            id="documents-tab"
                            class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                            aria-selected="false" role="tab">
                        <i class="fas fa-file-alt mr-2"></i>
                        Documents (<?php echo e($business->documents->count()); ?>)
                    </button>
                    <button onclick="switchTab('activity')"
                            id="activity-tab"
                            class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                            aria-selected="false" role="tab">
                        <i class="fas fa-history mr-2"></i>
                        Activity (0)
                    </button>
                </nav>
            </div>

            <!-- Tab Content -->
            <div class="p-6">
                <!-- Business Info Tab -->
                <div id="business-info-content" class="tab-pane">
                    <div class="space-y-6">
                        <!-- Business Information Section -->
                        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
                            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Business Information</h3>
                            </div>
                            <div class="px-6 py-4">
                                <dl class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Business Name</dt>
                                        <dd class="mt-1 text-sm text-gray-900 dark:text-white"><?php echo e($business->name); ?></dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Email</dt>
                                        <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                                            <?php if($business->email): ?>
                                                <a href="mailto:<?php echo e($business->email); ?>" class="text-blue-600 dark:text-blue-400 hover:text-blue-800">
                                                    <?php echo e($business->email); ?>

                                                </a>
                                            <?php else: ?>
                                                <span class="text-gray-400">Not provided</span>
                                            <?php endif; ?>
                                        </dd>
                                    </div>
                                    <?php if($business->description): ?>
                                        <div class="md:col-span-2">
                                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Description</dt>
                                            <dd class="mt-1 text-sm text-gray-900 dark:text-white"><?php echo e($business->description); ?></dd>
                                        </div>
                                    <?php endif; ?>
                                    <?php if($business->secondary_phone): ?>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Secondary Phone</dt>
                                            <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                                                <a href="tel:<?php echo e($business->secondary_phone); ?>" class="text-blue-600 dark:text-blue-400 hover:text-blue-800">
                                                    <?php echo e($business->secondary_phone); ?>

                                                </a>
                                            </dd>
                                        </div>
                                    <?php endif; ?>
                                    <?php if($business->secondary_website): ?>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Secondary Website</dt>
                                            <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                                                <a href="<?php echo e($business->secondary_website); ?>" target="_blank" class="text-blue-600 dark:text-blue-400 hover:text-blue-800">
                                                    <?php echo e($business->secondary_website); ?>

                                                    <i class="fas fa-external-link-alt ml-1 text-xs"></i>
                                                </a>
                                            </dd>
                                        </div>
                                    <?php endif; ?>
                                    <?php if($business->address): ?>
                                        <div class="md:col-span-2">
                                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Address</dt>
                                            <dd class="mt-1 text-sm text-gray-900 dark:text-white"><?php echo e($business->address); ?></dd>
                                        </div>
                                    <?php endif; ?>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Business Status</dt>
                                        <dd class="mt-1">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                                <?php echo e(ucfirst($business->status ?? 'Active')); ?>

                                            </span>
                                        </dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Created By</dt>
                                        <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                                            <?php echo e($business->creator->name ?? 'Administrator'); ?>

                                        </dd>
                                    </div>
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Created Date</dt>
                                        <dd class="mt-1 text-sm text-gray-900 dark:text-white"><?php echo e($business->created_at->format('M d, Y')); ?></dd>
                                    </div>
                                </dl>
                            </div>
                        </div>

                        <!-- Tags & Products Section -->
                        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
                            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Tags & Products</h3>
                            </div>
                            <div class="px-6 py-4 space-y-6">
                                <!-- Business Tags -->
                                <div>
                                    <div class="flex items-center justify-between mb-3">
                                        <h4 class="text-sm font-medium text-gray-900 dark:text-white">Business Tags</h4>
                                        <?php if(auth()->user()->hasPermission('manage_businesses')): ?>
                                            <a href="#" class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800">Manage Tags</a>
                                        <?php endif; ?>
                                    </div>
                                    <div class="flex flex-wrap gap-2">
                                        <?php if($business->tags && $business->tags->count() > 0): ?>
                                            <?php $__currentLoopData = $business->tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                                    <?php echo e($tag->name); ?>

                                                </span>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php else: ?>
                                            <!-- Default tags for demo -->
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                                Enterprise
                                            </span>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                                SaaS
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <!-- Business Products -->
                                <div>
                                    <div class="flex items-center justify-between mb-3">
                                        <h4 class="text-sm font-medium text-gray-900 dark:text-white">Business Products</h4>
                                        <?php if(auth()->user()->hasPermission('manage_businesses')): ?>
                                            <a href="#" class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800">Manage Products</a>
                                        <?php endif; ?>
                                    </div>
                                    <div class="flex flex-wrap gap-2">
                                        <?php if($business->products && $business->products->count() > 0): ?>
                                            <?php $__currentLoopData = $business->products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                                    <?php echo e($product->name); ?>

                                                </span>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php else: ?>
                                            <!-- Default product for demo -->
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                                whatsapp business api
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Danger Zone -->
                        <?php if(auth()->user()->hasPermission('manage_businesses')): ?>
                            <div class="bg-white dark:bg-gray-800 border border-red-200 dark:border-red-700 rounded-lg">
                                <div class="px-6 py-4 border-b border-red-200 dark:border-red-700">
                                    <h3 class="text-lg font-medium text-red-900 dark:text-red-400">Danger Zone</h3>
                                </div>
                                <div class="px-6 py-4">
                                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                                        Permanently delete this business and all associated data. This action cannot be undone.
                                    </p>
                                    <form method="POST" action="<?php echo e(route('business.destroy', $business)); ?>"
                                          onsubmit="return confirm('Are you sure you want to delete this business? This action cannot be undone.')">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit"
                                                class="bg-red-600 dark:bg-red-700 hover:bg-red-700 dark:hover:bg-red-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                                            <i class="fas fa-trash mr-2"></i>
                                            Delete Business
                                        </button>
                                    </form>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Contacts Tab -->
                <div id="contacts-content" class="tab-pane hidden">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Business Contacts</h3>
                    <?php if($business->contacts->count() > 0): ?>
                        <div class="space-y-4">
                            <?php $__currentLoopData = $business->contacts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $contact): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                    <div class="flex items-center">
                                        <div class="h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center mr-4">
                                            <i class="fas fa-user text-gray-600 dark:text-gray-400"></i>
                                        </div>
                                        <div>
                                            <h4 class="text-sm font-medium text-gray-900 dark:text-white">
                                                <?php echo e($contact->name); ?>

                                                <?php if($contact->is_primary): ?>
                                                    <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                                                        Primary
                                                    </span>
                                                <?php endif; ?>
                                            </h4>
                                            <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo e($contact->position ?: 'No position specified'); ?></p>
                                            <div class="flex items-center space-x-4 mt-1">
                                                <?php if($contact->email): ?>
                                                    <span class="text-xs text-gray-400">
                                                        <i class="fas fa-envelope mr-1"></i><?php echo e($contact->email); ?>

                                                    </span>
                                                <?php endif; ?>
                                                <?php if($contact->phone): ?>
                                                    <span class="text-xs text-gray-400">
                                                        <i class="fas fa-phone mr-1"></i><?php echo e($contact->phone); ?>

                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php else: ?>
                        <div class="px-6 py-12 text-center">
                            <i class="fas fa-users text-gray-400 text-4xl mb-4"></i>
                            <p class="text-gray-500 text-lg">No contacts added yet.</p>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Documents Tab -->
                <div id="documents-content" class="tab-pane hidden">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Business Documents</h3>
                    <?php if($business->documents->count() > 0): ?>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <?php $__currentLoopData = $business->documents; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $document): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <div class="h-10 w-10 rounded-lg bg-gray-200 dark:bg-gray-600 flex items-center justify-center">
                                                <i class="fas fa-file text-gray-600 dark:text-gray-400"></i>
                                            </div>
                                        </div>
                                        <div class="ml-4 flex-1 min-w-0">
                                            <div class="flex items-center justify-between">
                                                <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                                                    <?php echo e($document->original_filename); ?>

                                                </p>
                                                <span class="text-xs text-gray-500 dark:text-gray-400"><?php echo e($document->formatted_file_size); ?></span>
                                            </div>
                                            <p class="text-xs text-gray-400 mt-1">
                                                Uploaded <?php echo e($document->created_at->diffForHumans()); ?>

                                            </p>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php else: ?>
                        <div class="px-6 py-12 text-center">
                            <i class="fas fa-file-alt text-gray-400 text-4xl mb-4"></i>
                            <p class="text-gray-500 text-lg">No documents uploaded yet.</p>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Activity Tab -->
                <div id="activity-content" class="tab-pane hidden">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Business Activity</h3>
                    <div class="px-6 py-12 text-center">
                        <i class="fas fa-history text-gray-400 text-4xl mb-4"></i>
                        <p class="text-gray-500 text-lg">No activities yet.</p>
                        <p class="text-gray-400 text-sm">Activity will appear here as you interact with this business.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Minimal, safe JavaScript -->
<script>
// Minimal switchTab function - defined first to ensure availability
function switchTab(tabName) {
    console.log('switchTab called with:', tabName);
    
    // Hide all tab panes
    const tabPanes = document.querySelectorAll('.tab-pane');
    tabPanes.forEach(pane => {
        pane.classList.add('hidden');
    });

    // Remove active state from all tab buttons
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
        button.classList.remove('border-blue-500', 'text-blue-600');
        button.classList.add('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
        button.setAttribute('aria-selected', 'false');
    });

    // Show selected tab pane
    const selectedPane = document.getElementById(tabName + '-content');
    if (selectedPane) {
        selectedPane.classList.remove('hidden');
    }

    // Add active state to selected tab button
    const selectedButton = document.getElementById(tabName + '-tab');
    if (selectedButton) {
        selectedButton.classList.remove('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
        selectedButton.classList.add('border-blue-500', 'text-blue-600');
        selectedButton.setAttribute('aria-selected', 'true');
    }

    // Update URL hash
    window.location.hash = tabName;
}

// Simple initialization
document.addEventListener('DOMContentLoaded', function() {
    console.log('Business detail page loaded successfully');
    
    // Set default tab
    const hash = window.location.hash.substring(1);
    if (hash && ['business-info', 'contacts', 'documents', 'activity'].includes(hash)) {
        switchTab(hash);
    } else {
        switchTab('business-info');
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/business/Views/show.blade.php ENDPATH**/ ?>