<?php $__env->startSection('content'); ?>
        <!-- Header -->
        <div class="mb-8">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                        <i class="fas fa-puzzle-piece mr-3 text-primary-600 dark:text-primary-400"></i>
                        Plugin Manager
                    </h1>
                    <p class="text-gray-600 dark:text-gray-300">Manage your application plugins and their dependencies</p>
                </div>
                <div class="flex space-x-3">
                    <a href="<?php echo e(route('plugins.install')); ?>" class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-md transition duration-150 ease-in-out">
                        <i class="fas fa-upload mr-2"></i>
                        Install Plugin
                    </a>
                    <a href="<?php echo e(route('plugins.refresh')); ?>" class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 dark:bg-gray-700 dark:hover:bg-gray-600 text-white font-medium rounded-md transition duration-150 ease-in-out">
                        <i class="fas fa-sync-alt mr-2"></i>
                        Refresh
                    </a>
                    <a href="<?php echo e(route('plugins.api')); ?>" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600 text-white font-medium rounded-md transition duration-150 ease-in-out" target="_blank">
                        <i class="fas fa-code mr-2"></i>
                        API
                    </a>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-cubes text-2xl text-blue-600 dark:text-blue-400"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Total Plugins</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($stats['total']); ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-2xl text-green-600 dark:text-green-400"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Enabled</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($stats['enabled']); ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-times-circle text-2xl text-red-600 dark:text-red-400"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Disabled</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($stats['disabled']); ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-shield-alt text-2xl text-blue-600 dark:text-blue-400"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">System</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($stats['system']); ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-puzzle-piece text-2xl text-purple-600 dark:text-purple-400"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Third-party</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($stats['third_party']); ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dependency Errors -->
        <?php if(!empty($dependencyErrors)): ?>
            <div class="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-red-400"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">Dependency Issues Detected</h3>
                        <div class="mt-2 text-sm text-red-700">
                            <ul class="list-disc pl-5 space-y-1">
                                <?php $__currentLoopData = $dependencyErrors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li><?php echo e($error); ?></li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Plugins List -->
        <?php if(empty($plugins)): ?>
            <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md">
                <div class="text-center py-12">
                    <i class="fas fa-puzzle-piece text-4xl text-gray-400 dark:text-gray-500 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Plugins Found</h3>
                    <p class="text-gray-500 dark:text-gray-400 mb-4">No plugins are currently installed in the plugins directory.</p>
                    <p class="text-sm text-gray-400 dark:text-gray-500">Create a plugin directory in <code class="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">plugins/</code> with a <code class="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">config.json</code> file to get started.</p>
                </div>
            </div>
        <?php else: ?>
            <?php $__currentLoopData = $categorizedPlugins; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category => $categoryPlugins): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md mb-6">
                    <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                        <div class="flex items-center">
                            <?php if($category === 'System'): ?>
                                <i class="fas fa-shield-alt text-blue-600 dark:text-blue-400 mr-3"></i>
                            <?php else: ?>
                                <i class="fas fa-puzzle-piece text-purple-600 dark:text-purple-400 mr-3"></i>
                            <?php endif; ?>
                            <div>
                                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white"><?php echo e($category); ?> Plugins</h3>
                                <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
                                    <?php if($category === 'System'): ?>
                                        Core system plugins required for application functionality
                                    <?php else: ?>
                                        Additional plugins that extend application features
                                    <?php endif; ?>
                                    (<?php echo e(count($categoryPlugins)); ?> <?php echo e(count($categoryPlugins) === 1 ? 'plugin' : 'plugins'); ?>)
                                </p>
                            </div>
                        </div>
                    </div>

                    <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                        <?php $__currentLoopData = $categoryPlugins; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plugin): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php echo $__env->make('plugins.partials.plugin-item', ['plugin' => $plugin], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/resources/views/plugins/index.blade.php ENDPATH**/ ?>