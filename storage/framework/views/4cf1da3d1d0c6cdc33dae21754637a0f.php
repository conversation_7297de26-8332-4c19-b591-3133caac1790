<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex justify-between items-start mb-6">
        <div>
            <div class="flex items-center gap-3 mb-2">
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white"><?php echo e($meeting->title); ?></h1>
                <span class="px-3 py-1 text-sm font-medium rounded-full <?php echo e($meeting->status_badge_class); ?>">
                    <?php echo e(ucfirst($meeting->status)); ?>

                </span>
            </div>
            <div class="flex items-center text-gray-600 dark:text-gray-400 gap-4">
                <span><i class="fas fa-calendar mr-2"></i><?php echo e($meeting->scheduled_at->format('M j, Y')); ?></span>
                <span><i class="fas fa-clock mr-2"></i><?php echo e($meeting->scheduled_at->format('g:i A')); ?></span>
                <span><i class="fas fa-hourglass-half mr-2"></i><?php echo e($meeting->formatted_duration); ?></span>
            </div>
        </div>
        <div class="flex gap-2">
            <a href="<?php echo e(route('meetings.index')); ?>" 
               class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                <i class="fas fa-arrow-left mr-2"></i>Back to Meetings
            </a>
            <?php if($meeting->canEdit(auth()->user())): ?>
                <button onclick="editMeeting()" 
                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    <i class="fas fa-edit mr-2"></i>Edit Meeting
                </button>
            <?php endif; ?>
        </div>
    </div>

    <!-- Tab Navigation -->
    <div class="mb-6">
        <nav class="flex space-x-8" aria-label="Tabs">
            <button type="button" onclick="switchDetailTab('overview')"
                    class="detail-tab-button active whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                    data-tab="overview">
                <i class="fas fa-info-circle mr-2"></i>Overview
            </button>
            <button type="button" onclick="switchDetailTab('agenda-minutes')"
                    class="detail-tab-button whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                    data-tab="agenda-minutes">
                <i class="fas fa-list-alt mr-2"></i>Agenda & Minutes
            </button>
            <button type="button" onclick="switchDetailTab('action-items')"
                    class="detail-tab-button whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                    data-tab="action-items">
                <i class="fas fa-tasks mr-2"></i>Action Items
            </button>
            <button type="button" onclick="switchDetailTab('attachments')"
                    class="detail-tab-button whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                    data-tab="attachments">
                <i class="fas fa-paperclip mr-2"></i>Attachments
            </button>
            <button type="button" onclick="switchDetailTab('recordings')"
                    class="detail-tab-button whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                    data-tab="recordings">
                <i class="fas fa-video mr-2"></i>Recordings
            </button>
        </nav>
    </div>

    <!-- Tab Content -->
    <div class="w-full">
        <!-- Main Content Area -->
            <!-- Overview Tab -->
            <div id="overview-tab" class="detail-tab-content">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 space-y-6">
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Meeting Details</h2>

                    <?php if($meeting->description): ?>
                        <div class="mb-4">
                            <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description</h3>
                            <div class="prose dark:prose-invert max-w-none">
                                <?php echo $meeting->description; ?>

                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if($meeting->location): ?>
                        <div class="mb-4">
                            <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Location</h3>
                            <p class="text-gray-900 dark:text-white flex items-center">
                                <i class="fas fa-map-marker-alt mr-2 text-gray-500"></i>
                                <?php echo e($meeting->location); ?>

                            </p>
                        </div>
                    <?php endif; ?>

                    <?php if($meeting->meeting_link): ?>
                        <div class="mb-4">
                            <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Meeting Link</h3>
                            <a href="<?php echo e($meeting->meeting_link); ?>" target="_blank"
                               class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 flex items-center">
                                <i class="fas fa-external-link-alt mr-2"></i>
                                Join Meeting
                            </a>
                        </div>
                    <?php endif; ?>

                    <!-- Participants Section in Overview -->
                    <div class="mt-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Participants</h3>
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                            <?php $__currentLoopData = $meeting->participants; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $participant): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                    <div class="w-8 h-8 <?php echo e($participant->isContact() ? 'bg-green-500' : 'bg-blue-500'); ?> rounded-full flex items-center justify-center mr-3">
                                        <?php if($participant->isContact()): ?>
                                            <i class="fas fa-address-book text-white text-sm"></i>
                                        <?php else: ?>
                                            <span class="text-sm font-medium text-white">
                                                <?php echo e(substr($participant->getParticipantName(), 0, 1)); ?>

                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="flex-1">
                                        <p class="font-medium text-gray-900 dark:text-white"><?php echo e($participant->getParticipantName()); ?></p>
                                        <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo e($participant->getParticipantEmail()); ?></p>
                                        <?php if($participant->isContact()): ?>
                                            <p class="text-xs text-green-600 dark:text-green-400">Contact</p>
                                        <?php else: ?>
                                            <p class="text-xs text-blue-600 dark:text-blue-400">System User</p>
                                        <?php endif; ?>
                                    </div>
                                    <span class="px-2 py-1 text-xs font-medium rounded-full <?php echo e($participant->status_badge_class); ?>">
                                        <?php echo e(ucfirst($participant->status)); ?>

                                    </span>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Agenda & Minutes Tab -->
            <div id="agenda-minutes-tab" class="detail-tab-content hidden">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 space-y-6">
                    <?php if($meeting->agenda): ?>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Meeting Agenda</h3>
                            <div class="prose dark:prose-invert max-w-none bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                <?php echo $meeting->agenda; ?>

                            </div>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-8">
                            <i class="fas fa-list-alt text-gray-400 text-4xl mb-3"></i>
                            <p class="text-gray-500 dark:text-gray-400">No agenda has been set for this meeting.</p>
                        </div>
                    <?php endif; ?>

                    <?php if($meeting->minutes): ?>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Meeting Minutes</h3>
                            <div class="prose dark:prose-invert max-w-none bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                <?php echo $meeting->minutes; ?>

                            </div>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-8">
                            <i class="fas fa-file-alt text-gray-400 text-4xl mb-3"></i>
                            <p class="text-gray-500 dark:text-gray-400">No meeting minutes have been recorded yet.</p>
                            <?php if($meeting->canEdit(auth()->user())): ?>
                                <button onclick="editMeeting()"
                                        class="mt-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                    Add meeting minutes
                                </button>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Action Items Tab -->
            <div id="action-items-tab" class="detail-tab-content hidden">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Action Items</h2>
                        <button onclick="showAddActionItemModal()"
                                class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-3 rounded text-sm">
                            <i class="fas fa-plus mr-1"></i>Add Action Item
                        </button>
                    </div>

                    <div id="actionItemsContainer">
                        <?php $__empty_1 = true; $__currentLoopData = $meeting->actionItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $actionItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-4 mb-3" data-action-item-id="<?php echo e($actionItem->id); ?>">
                                <div class="flex justify-between items-start">
                                    <div class="flex-1">
                                        <div class="flex items-center gap-3 mb-2">
                                            <h4 class="font-medium text-gray-900 dark:text-white"><?php echo e($actionItem->title); ?></h4>
                                            <span class="px-2 py-1 text-xs font-medium rounded-full <?php echo e($actionItem->status_badge_class); ?>">
                                                <?php echo e(ucfirst($actionItem->status)); ?>

                                            </span>
                                            <span class="px-2 py-1 text-xs font-medium rounded-full <?php echo e($actionItem->priority_badge_class); ?>">
                                                <?php echo e(ucfirst($actionItem->priority)); ?>

                                            </span>
                                        </div>

                                        <?php if($actionItem->description): ?>
                                            <p class="text-gray-600 dark:text-gray-400 text-sm mb-2"><?php echo e($actionItem->description); ?></p>
                                        <?php endif; ?>

                                        <div class="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                                            <?php if($actionItem->assignee): ?>
                                                <span><i class="fas fa-user mr-1"></i><?php echo e($actionItem->assignee->name); ?></span>
                                            <?php endif; ?>
                                            <?php if($actionItem->due_date): ?>
                                                <span><i class="fas fa-calendar mr-1"></i><?php echo e($actionItem->due_date_status); ?></span>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <div class="flex items-center gap-2">
                                        <button onclick="toggleActionItem(<?php echo e($actionItem->id); ?>)"
                                                class="text-<?php echo e($actionItem->status === 'completed' ? 'green' : 'gray'); ?>-600 hover:text-<?php echo e($actionItem->status === 'completed' ? 'green' : 'gray'); ?>-800">
                                            <i class="fas fa-<?php echo e($actionItem->status === 'completed' ? 'check-circle' : 'circle'); ?>"></i>
                                        </button>
                                        <button onclick="editActionItem(<?php echo e($actionItem->id); ?>)"
                                                class="text-blue-600 hover:text-blue-800">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button onclick="deleteActionItem(<?php echo e($actionItem->id); ?>)"
                                                class="text-red-600 hover:text-red-800">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <div class="text-center py-8">
                                <i class="fas fa-tasks text-gray-400 text-4xl mb-3"></i>
                                <p class="text-gray-500 dark:text-gray-400">No action items yet.</p>
                                <button onclick="showAddActionItemModal()"
                                        class="mt-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                    Add the first action item
                                </button>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Attachments Tab -->
            <div id="attachments-tab" class="detail-tab-content hidden">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Attachments</h2>
                        <button onclick="showUploadModal()"
                                class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-3 rounded text-sm">
                            <i class="fas fa-upload mr-1"></i>Upload File
                        </button>
                    </div>

                    <div id="attachmentsContainer">
                        <?php $__empty_1 = true; $__currentLoopData = $meeting->attachments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attachment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <div class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded-lg mb-3" data-attachment-id="<?php echo e($attachment->id); ?>">
                                <div class="flex items-center">
                                    <i class="<?php echo e($attachment->file_icon_class); ?> mr-3"></i>
                                    <div>
                                        <p class="font-medium text-gray-900 dark:text-white"><?php echo e($attachment->original_filename); ?></p>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">
                                            <?php echo e($attachment->formatted_file_size); ?> •
                                            <?php echo e(ucfirst($attachment->category)); ?> •
                                            Uploaded by <?php echo e($attachment->uploader->name); ?>

                                        </p>
                                    </div>
                                </div>
                                <div class="flex items-center gap-2">
                                    <a href="<?php echo e(route('meetings.attachments.download', [$meeting, $attachment])); ?>"
                                       class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                        <i class="fas fa-download"></i>
                                    </a>
                                    <?php if($attachment->uploaded_by === auth()->id() || auth()->user()->hasPermission('manage_meetings')): ?>
                                        <button onclick="deleteAttachment(<?php echo e($attachment->id); ?>)"
                                                class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <div class="text-center py-8">
                                <i class="fas fa-paperclip text-gray-400 text-4xl mb-3"></i>
                                <p class="text-gray-500 dark:text-gray-400">No attachments yet.</p>
                                <button onclick="showUploadModal()"
                                        class="mt-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                    Upload the first file
                                </button>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Recordings Tab -->
            <div id="recordings-tab" class="detail-tab-content hidden">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Meeting Recordings</h2>
                        <button onclick="showRecordingUploadModal()"
                                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-3 rounded text-sm">
                            <i class="fas fa-video mr-1"></i>Upload Recording
                        </button>
                    </div>

                    <div id="recordingsContainer">
                        <?php
                            $recordings = $meeting->attachments->where('attachment_type', 'video');
                        ?>

                        <?php $__empty_1 = true; $__currentLoopData = $recordings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $recording): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-4 mb-4" data-recording-id="<?php echo e($recording->id); ?>">
                                <div class="flex items-start justify-between mb-3">
                                    <div class="flex items-center">
                                        <div class="w-12 h-12 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center mr-4">
                                            <i class="fas fa-video text-red-600 dark:text-red-400 text-xl"></i>
                                        </div>
                                        <div>
                                            <h4 class="font-medium text-gray-900 dark:text-white"><?php echo e($recording->original_filename); ?></h4>
                                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                                <?php echo e($recording->formatted_file_size); ?> •
                                                Uploaded by <?php echo e($recording->uploader->name); ?> •
                                                <?php echo e($recording->created_at->format('M j, Y g:i A')); ?>

                                            </p>
                                            <?php if($recording->description): ?>
                                                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1"><?php echo e($recording->description); ?></p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <button onclick="downloadRecording(<?php echo e($recording->id); ?>)"
                                                class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                            <i class="fas fa-download"></i>
                                        </button>
                                        <?php if($recording->uploaded_by === auth()->id() || auth()->user()->hasPermission('manage_meetings')): ?>
                                            <button onclick="deleteRecording(<?php echo e($recording->id); ?>)"
                                                    class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <!-- Video Player -->
                                <div class="bg-black rounded-lg overflow-hidden relative" id="video-container-<?php echo e($recording->id); ?>">
                                    <!-- Loading indicator -->
                                    <div class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-75 z-10"
                                         id="video-loading-<?php echo e($recording->id); ?>">
                                        <div class="text-white text-center">
                                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
                                            <p class="text-sm">Loading video...</p>
                                        </div>
                                    </div>

                                    <video controls class="w-full h-64 object-contain" preload="metadata"
                                           onerror="handleVideoError(this, <?php echo e($recording->id); ?>)"
                                           onloadstart="showVideoLoading(<?php echo e($recording->id); ?>)"
                                           oncanplay="hideVideoLoading(<?php echo e($recording->id); ?>)"
                                           onloadeddata="hideVideoLoading(<?php echo e($recording->id); ?>)">
                                        <?php
                                            // Map MIME types to browser-compatible types
                                            $browserMimeType = match($recording->mime_type) {
                                                'video/x-matroska' => 'video/webm',
                                                default => $recording->mime_type
                                            };
                                        ?>
                                        <source src="<?php echo e(route('meetings.attachments.stream', [$meeting, $recording])); ?>" type="<?php echo e($browserMimeType); ?>">
                                        <!-- Fallback for unsupported browsers -->
                                        <p class="text-white p-4">
                                            Your browser does not support the video tag.
                                            <a href="<?php echo e(route('meetings.attachments.download', [$meeting, $recording])); ?>"
                                               class="text-blue-400 hover:text-blue-300 underline">
                                                Download the video file
                                            </a>
                                        </p>
                                    </video>
                                </div>

                                <!-- Recording Controls -->
                                <div class="flex items-center justify-between mt-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                    <div class="flex items-center gap-4">
                                        <span class="text-sm text-gray-600 dark:text-gray-400">
                                            <i class="fas fa-eye mr-1"></i><?php echo e($recording->download_count); ?> views
                                        </span>
                                        <?php if($recording->is_public): ?>
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                                <i class="fas fa-globe mr-1"></i>Public
                                            </span>
                                        <?php else: ?>
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">
                                                <i class="fas fa-lock mr-1"></i>Private
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    <button onclick="shareRecording(<?php echo e($recording->id); ?>)"
                                            class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm">
                                        <i class="fas fa-share mr-1"></i>Share
                                    </button>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <div class="text-center py-12">
                                <i class="fas fa-video text-gray-400 text-6xl mb-4"></i>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No recordings yet</h3>
                                <p class="text-gray-500 dark:text-gray-400 mb-4">Upload meeting recordings to share with participants.</p>
                                <button onclick="showRecordingUploadModal()"
                                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                                    <i class="fas fa-video mr-2"></i>Upload First Recording
                                </button>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Meeting Info Card (moved to bottom, full width) -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mt-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Meeting Information</h3>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-500 dark:text-gray-400">Created by:</span>
                        <span class="text-gray-900 dark:text-white"><?php echo e($meeting->creator->name); ?></span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-500 dark:text-gray-400">Created:</span>
                        <span class="text-gray-900 dark:text-white"><?php echo e($meeting->created_at->format('M j, Y')); ?></span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-500 dark:text-gray-400">Last updated:</span>
                        <span class="text-gray-900 dark:text-white"><?php echo e($meeting->updated_at->format('M j, Y')); ?></span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-500 dark:text-gray-400">Participants:</span>
                        <span class="text-gray-900 dark:text-white"><?php echo e($meeting->participant_count); ?></span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-500 dark:text-gray-400">Attachments:</span>
                        <span class="text-gray-900 dark:text-white"><?php echo e($meeting->attachment_count); ?></span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-500 dark:text-gray-400">Action Items:</span>
                        <span class="text-gray-900 dark:text-white"><?php echo e($meeting->action_item_count); ?></span>
                    </div>
                </div>
            </div>
        </div>


    </div>
</div>

<!-- Action Item Modal -->
<div id="actionItemModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex justify-between items-center pb-4 border-b border-gray-200 dark:border-gray-600">
                <h3 id="actionItemModalTitle" class="text-lg font-semibold text-gray-900 dark:text-white">Add Action Item</h3>
                <button onclick="closeActionItemModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Modal Body -->
            <form id="actionItemForm" class="mt-4">
                <input type="hidden" id="actionItemId" name="id">

                <div class="space-y-4">
                    <!-- Title -->
                    <div>
                        <label for="actionItemTitle" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Title *</label>
                        <input type="text" id="actionItemTitle" name="title" required
                               class="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    </div>

                    <!-- Description -->
                    <div>
                        <label for="actionItemDescription" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description</label>
                        <textarea id="actionItemDescription" name="description" rows="3"
                                  class="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"></textarea>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <!-- Assigned To -->
                        <div>
                            <label for="actionItemAssignedTo" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Assigned To</label>
                            <select id="actionItemAssignedTo" name="assigned_to" class="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                                <option value="">Select assignee...</option>
                                <?php $__currentLoopData = $meeting->participants; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $participant): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($participant->isUser()): ?>
                                        <option value="<?php echo e($participant->user->id); ?>"><?php echo e($participant->getParticipantName()); ?></option>
                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>

                        <!-- Priority -->
                        <div>
                            <label for="actionItemPriority" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Priority</label>
                            <select id="actionItemPriority" name="priority" class="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                                <option value="low">Low</option>
                                <option value="medium" selected>Medium</option>
                                <option value="high">High</option>
                            </select>
                        </div>

                        <!-- Due Date -->
                        <div>
                            <label for="actionItemDueDate" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Due Date</label>
                            <input type="date" id="actionItemDueDate" name="due_date"
                                   class="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                        </div>
                    </div>

                    <!-- Status (for editing) -->
                    <div id="actionItemStatusField" class="hidden">
                        <label for="actionItemStatus" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</label>
                        <select id="actionItemStatus" name="status" class="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                            <option value="pending">Pending</option>
                            <option value="in_progress">In Progress</option>
                            <option value="completed">Completed</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                    </div>

                    <!-- Notes (for editing) -->
                    <div id="actionItemNotesField" class="hidden">
                        <label for="actionItemNotes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Notes</label>
                        <textarea id="actionItemNotes" name="notes" rows="2"
                                  class="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"></textarea>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="mt-6 flex justify-end space-x-3">
                    <button type="button" onclick="closeActionItemModal()"
                            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                        Cancel
                    </button>
                    <button type="submit" id="actionItemSubmitButton"
                            class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                        <span id="actionItemSubmitButtonText">Add Action Item</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- File Upload Modal -->
<div id="uploadModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex justify-between items-center pb-4 border-b border-gray-200 dark:border-gray-600">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Upload File</h3>
                <button onclick="closeUploadModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Modal Body -->
            <form id="uploadForm" class="mt-4" enctype="multipart/form-data">
                <div class="space-y-4">
                    <!-- File Upload -->
                    <div>
                        <label for="attachmentFile" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">File *</label>
                        <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400 transition-colors">
                            <div class="space-y-1 text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                                <div class="flex text-sm text-gray-600 dark:text-gray-400">
                                    <label for="attachmentFile" class="relative cursor-pointer bg-white dark:bg-gray-800 rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                        <span>Upload a file</span>
                                        <input id="attachmentFile" name="file" type="file" class="sr-only" required>
                                    </label>
                                    <p class="pl-1">or drag and drop</p>
                                </div>
                                <p class="text-xs text-gray-500 dark:text-gray-400">
                                    Documents, images, audio, video up to 10MB
                                </p>
                            </div>
                        </div>
                        <div id="filePreview" class="mt-3 hidden">
                            <div class="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                                <i id="fileIcon" class="fas fa-file text-gray-400 mr-3"></i>
                                <div class="flex-1">
                                    <p id="fileName" class="text-sm font-medium text-gray-900 dark:text-white"></p>
                                    <p id="fileSize" class="text-xs text-gray-500 dark:text-gray-400"></p>
                                </div>
                                <button type="button" onclick="removeFile()" class="text-red-600 hover:text-red-800">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Category -->
                        <div>
                            <label for="attachmentCategory" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Category</label>
                            <select id="attachmentCategory" name="category" class="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                                <option value="pre_meeting">Pre-meeting</option>
                                <option value="post_meeting">Post-meeting</option>
                                <option value="minutes">Meeting Minutes</option>
                                <option value="other" selected>Other</option>
                            </select>
                        </div>

                        <!-- Public -->
                        <div class="flex items-center">
                            <input type="checkbox" id="attachmentIsPublic" name="is_public" value="1"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="attachmentIsPublic" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                Make file public (visible to all participants)
                            </label>
                        </div>
                    </div>

                    <!-- Description -->
                    <div>
                        <label for="attachmentDescription" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description</label>
                        <textarea id="attachmentDescription" name="description" rows="2" placeholder="Optional description of the file"
                                  class="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"></textarea>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="mt-6 flex justify-end space-x-3">
                    <button type="button" onclick="closeUploadModal()"
                            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                        Cancel
                    </button>
                    <button type="submit" id="uploadSubmitButton"
                            class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
                        <span id="uploadSubmitButtonText">Upload File</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Recording Upload Modal -->
<div id="recordingUploadModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-lg shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex justify-between items-center pb-4 border-b border-gray-200 dark:border-gray-600">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Upload Meeting Recording</h3>
                <button onclick="closeRecordingUploadModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Modal Body -->
            <form id="recordingUploadForm" enctype="multipart/form-data" class="mt-4">
                <?php echo csrf_field(); ?>
                <input type="hidden" name="meeting_id" value="<?php echo e($meeting->id); ?>">
                <input type="hidden" name="category" value="recording">
                <input type="hidden" name="attachment_type" value="video">

                <!-- File Upload -->
                <div class="mb-4">
                    <label for="recordingFile" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Recording File *
                    </label>
                    <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-md">
                        <div class="space-y-1 text-center">
                            <i class="fas fa-video text-gray-400 text-3xl mb-3"></i>
                            <div class="flex text-sm text-gray-600 dark:text-gray-400">
                                <label for="recordingFile" class="relative cursor-pointer bg-white dark:bg-gray-800 rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                    <span>Upload a recording</span>
                                    <input id="recordingFile" name="file" type="file" class="sr-only" accept="video/mp4,video/mov,video/avi,video/webm,video/*" required>
                                </label>
                                <p class="pl-1">or drag and drop</p>
                            </div>
                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                Supports MP4, MOV, AVI, and WebM formats up to 500MB
                            </p>
                        </div>
                    </div>
                    <div id="recordingFileInfo" class="mt-2 hidden">
                        <div class="flex items-center p-2 bg-blue-50 dark:bg-blue-900 rounded-md">
                            <i class="fas fa-video text-blue-600 dark:text-blue-400 mr-2"></i>
                            <span id="recordingFileName" class="text-sm text-blue-800 dark:text-blue-200"></span>
                            <button type="button" onclick="clearRecordingFile()" class="ml-auto text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Description -->
                <div class="mb-4">
                    <label for="recordingDescription" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Description
                    </label>
                    <textarea id="recordingDescription" name="description" rows="3"
                              placeholder="Optional description of the recording content"
                              class="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"></textarea>
                </div>

                <!-- Privacy Settings -->
                <div class="mb-4">
                    <div class="flex items-center">
                        <input id="recordingIsPublic" name="is_public" type="checkbox"
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="recordingIsPublic" class="ml-2 block text-sm text-gray-900 dark:text-white">
                            Make recording public (visible to all participants)
                        </label>
                    </div>
                </div>

                <!-- Upload Progress -->
                <div id="recordingUploadProgress" class="mb-4 hidden">
                    <div class="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                        <span>Uploading...</span>
                        <span id="recordingUploadPercent">0%</span>
                    </div>
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div id="recordingUploadBar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                </div>
            </form>

            <!-- Modal Actions -->
            <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600">
                <button type="button" onclick="closeRecordingUploadModal()"
                        class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                    Cancel
                </button>
                <button type="button" onclick="uploadRecording()" id="recordingUploadBtn"
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                    <i class="fas fa-video mr-2"></i>Upload Recording
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Tab Styles -->
<style>
.detail-tab-button {
    border-bottom-color: transparent;
    color: #6B7280;
    transition: all 0.2s ease-in-out;
}

.detail-tab-button:hover {
    color: #374151;
    border-bottom-color: #D1D5DB;
}

.detail-tab-button.active {
    color: #3B82F6;
    border-bottom-color: #3B82F6;
}

.dark .detail-tab-button {
    color: #9CA3AF;
}

.dark .detail-tab-button:hover {
    color: #D1D5DB;
    border-bottom-color: #4B5563;
}

.dark .detail-tab-button.active {
    color: #60A5FA;
    border-bottom-color: #60A5FA;
}

.detail-tab-content {
    min-height: 400px;
}

@media (max-width: 768px) {
    .detail-tab-button {
        font-size: 0.75rem;
        padding: 0.5rem 0.25rem;
        flex: 1;
        text-align: center;
    }

    .detail-tab-button i {
        display: none;
    }

    nav[aria-label="Tabs"] {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    nav[aria-label="Tabs"]::-webkit-scrollbar {
        display: none;
    }

    .detail-tab-content {
        min-height: 300px;
    }

    /* Stack grid layout on mobile */
    .grid.lg\\:grid-cols-3 {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 640px) {
    .detail-tab-button {
        font-size: 0.7rem;
        padding: 0.4rem 0.2rem;
    }

    /* Adjust participant grid on mobile */
    .grid.sm\\:grid-cols-2 {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
const meeting = <?php echo json_encode($meeting, 15, 512) ?>;

// Detail page tab management
let currentDetailTab = 'overview';
const detailTabOrder = ['overview', 'agenda-minutes', 'action-items', 'attachments'];

function switchDetailTab(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.detail-tab-content').forEach(tab => {
        tab.classList.add('hidden');
    });

    // Remove active class from all tab buttons
    document.querySelectorAll('.detail-tab-button').forEach(button => {
        button.classList.remove('active');
    });

    // Show selected tab content
    document.getElementById(tabName + '-tab').classList.remove('hidden');

    // Add active class to selected tab button
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

    // Update current tab
    currentDetailTab = tabName;
}

// Initialize detail page
document.addEventListener('DOMContentLoaded', function() {
    // Set up keyboard navigation
    document.addEventListener('keydown', function(e) {
        // Tab navigation with Ctrl/Cmd + arrow keys
        if ((e.ctrlKey || e.metaKey) && (e.key === 'ArrowLeft' || e.key === 'ArrowRight')) {
            e.preventDefault();

            const currentIndex = detailTabOrder.indexOf(currentDetailTab);
            let newIndex;

            if (e.key === 'ArrowLeft') {
                newIndex = currentIndex > 0 ? currentIndex - 1 : detailTabOrder.length - 1;
            } else {
                newIndex = currentIndex < detailTabOrder.length - 1 ? currentIndex + 1 : 0;
            }

            switchDetailTab(detailTabOrder[newIndex]);
        }
    });

    // Initialize first tab
    switchDetailTab('overview');
});

function editMeeting() {
    window.location.href = `/meetings?edit=${meeting.id}`;
}

function showAddActionItemModal() {
    document.getElementById('actionItemModalTitle').textContent = 'Add Action Item';
    document.getElementById('actionItemSubmitButtonText').textContent = 'Add Action Item';
    document.getElementById('actionItemForm').reset();
    document.getElementById('actionItemId').value = '';

    // Hide editing-only fields
    document.getElementById('actionItemStatusField').classList.add('hidden');
    document.getElementById('actionItemNotesField').classList.add('hidden');

    // Set default due date to one week from now
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);
    document.getElementById('actionItemDueDate').value = nextWeek.toISOString().split('T')[0];

    document.getElementById('actionItemModal').classList.remove('hidden');
}

function closeActionItemModal() {
    document.getElementById('actionItemModal').classList.add('hidden');
}

function editActionItem(id) {
    // Implementation for editing action item
    alert('Edit action item: ' + id);
}

function deleteActionItem(id) {
    if (!confirm('Are you sure you want to delete this action item?')) {
        return;
    }
    
    fetch(`/meetings/${meeting.id}/action-items/${id}`, {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.querySelector(`[data-action-item-id="${id}"]`).remove();
            showNotification(data.message, 'success');
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred while deleting the action item.', 'error');
    });
}

function toggleActionItem(id) {
    fetch(`/meetings/${meeting.id}/action-items/${id}/toggle`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update the UI to reflect the new status
            location.reload(); // Simple reload for now
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred while updating the action item.', 'error');
    });
}

function showUploadModal() {
    document.getElementById('uploadForm').reset();
    document.getElementById('filePreview').classList.add('hidden');
    document.getElementById('uploadModal').classList.remove('hidden');
}

function closeUploadModal() {
    document.getElementById('uploadModal').classList.add('hidden');
}

// Add event listeners when document loads
document.addEventListener('DOMContentLoaded', function() {
    // Load server upload limits
    fetchUploadLimits();

    // Action item form submission
    document.getElementById('actionItemForm').addEventListener('submit', function(e) {
        e.preventDefault();
        submitActionItem();
    });

    // Upload form submission
    document.getElementById('uploadForm').addEventListener('submit', function(e) {
        e.preventDefault();
        submitUpload();
    });

    // File input change
    document.getElementById('attachmentFile').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            showFilePreview(file);
        }
    });

    // Drag and drop functionality
    const dropZone = document.querySelector('#uploadModal .border-dashed');
    if (dropZone) {
        dropZone.addEventListener('dragover', function(e) {
            e.preventDefault();
            dropZone.classList.add('border-blue-500', 'bg-blue-50');
        });

        dropZone.addEventListener('dragleave', function(e) {
            e.preventDefault();
            dropZone.classList.remove('border-blue-500', 'bg-blue-50');
        });

        dropZone.addEventListener('drop', function(e) {
            e.preventDefault();
            dropZone.classList.remove('border-blue-500', 'bg-blue-50');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                document.getElementById('attachmentFile').files = files;
                showFilePreview(files[0]);
            }
        });
    }

    // Recording file input handler
    const recordingFileInput = document.getElementById('recordingFile');
    if (recordingFileInput) {
        recordingFileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                // Validate file type
                const allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/webm'];
                const allowedExtensions = ['.mp4', '.avi', '.mov', '.webm'];

                const isValidType = allowedTypes.includes(file.type) ||
                                  allowedExtensions.some(ext => file.name.toLowerCase().endsWith(ext));

                if (!isValidType) {
                    showNotification('Please select a valid video file (MP4, MOV, AVI, or WebM)', 'error');
                    recordingFileInput.value = '';
                    return;
                }

                // Validate file size using server limits for video files
                const maxSize = serverUploadLimits ? serverUploadLimits.video_max_file_size : 524288000; // Default to 500MB
                const maxSizeMB = serverUploadLimits ? serverUploadLimits.video_max_file_size_mb : 500;

                if (file.size > maxSize) {
                    showNotification(`File size must be less than ${maxSizeMB}MB`, 'error');
                    recordingFileInput.value = '';
                    return;
                }

                document.getElementById('recordingFileName').textContent = file.name;
                document.getElementById('recordingFileInfo').classList.remove('hidden');
            }
        });
    }
});

function submitActionItem() {
    const formData = new FormData(document.getElementById('actionItemForm'));
    const data = Object.fromEntries(formData.entries());

    const isEditing = document.getElementById('actionItemId').value !== '';
    const url = isEditing
        ? `/meetings/${meeting.id}/action-items/${document.getElementById('actionItemId').value}`
        : `/meetings/${meeting.id}/action-items`;

    if (isEditing) {
        data._method = 'PUT';
    }

    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeActionItemModal();
            showNotification(data.message, 'success');
            // Reload page to show updated action items
            location.reload();
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred while saving the action item.', 'error');
    });
}

function submitUpload() {
    const formData = new FormData(document.getElementById('uploadForm'));

    fetch(`/meetings/${meeting.id}/attachments`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeUploadModal();
            showNotification(data.message, 'success');
            // Reload page to show new attachment
            location.reload();
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred while uploading the file.', 'error');
    });
}

function showFilePreview(file) {
    const preview = document.getElementById('filePreview');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');
    const fileIcon = document.getElementById('fileIcon');

    fileName.textContent = file.name;
    fileSize.textContent = formatFileSize(file.size);

    // Set appropriate icon based on file type
    const extension = file.name.split('.').pop().toLowerCase();
    fileIcon.className = getFileIconClass(extension);

    preview.classList.remove('hidden');
}

function removeFile() {
    document.getElementById('attachmentFile').value = '';
    document.getElementById('filePreview').classList.add('hidden');
}

function formatFileSize(bytes) {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
}

function getFileIconClass(extension) {
    const iconMap = {
        'pdf': 'fas fa-file-pdf text-red-500',
        'doc': 'fas fa-file-word text-blue-500',
        'docx': 'fas fa-file-word text-blue-500',
        'xls': 'fas fa-file-excel text-green-500',
        'xlsx': 'fas fa-file-excel text-green-500',
        'ppt': 'fas fa-file-powerpoint text-orange-500',
        'pptx': 'fas fa-file-powerpoint text-orange-500',
        'txt': 'fas fa-file-alt text-gray-500',
        'jpg': 'fas fa-image text-green-500',
        'jpeg': 'fas fa-image text-green-500',
        'png': 'fas fa-image text-green-500',
        'gif': 'fas fa-image text-green-500',
        'mp3': 'fas fa-music text-purple-500',
        'wav': 'fas fa-music text-purple-500',
        'mp4': 'fas fa-video text-red-500',
        'avi': 'fas fa-video text-red-500',
        'mov': 'fas fa-video text-red-500',
        'webm': 'fas fa-video text-red-500',
        'zip': 'fas fa-file-archive text-yellow-500',
        'rar': 'fas fa-file-archive text-yellow-500',
    };

    return iconMap[extension] || 'fas fa-file text-gray-400';
}

function deleteAttachment(id) {
    if (!confirm('Are you sure you want to delete this attachment?')) {
        return;
    }
    
    fetch(`/meetings/${meeting.id}/attachments/${id}`, {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.querySelector(`[data-attachment-id="${id}"]`).remove();
            showNotification(data.message, 'success');
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred while deleting the attachment.', 'error');
    });
}

// Recording management functions
let serverUploadLimits = null;

function showRecordingUploadModal() {
    document.getElementById('recordingUploadModal').classList.remove('hidden');

    // Fetch server upload limits if not already loaded
    if (!serverUploadLimits) {
        fetchUploadLimits();
    }
}

function fetchUploadLimits() {
    fetch('/meetings/api/upload-limits')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                serverUploadLimits = data.limits;
                updateUploadLimitText();
            }
        })
        .catch(error => {
            console.error('Failed to fetch upload limits:', error);
        });
}

function updateUploadLimitText() {
    if (serverUploadLimits) {
        const limitText = document.querySelector('#recordingUploadModal .text-xs.text-gray-500');
        if (limitText) {
            const videoLimitMB = serverUploadLimits.video_max_file_size_mb || 500;
            limitText.textContent = `Supports MP4, MOV, AVI, and WebM formats up to ${videoLimitMB}MB`;
        }
    }
}

function closeRecordingUploadModal() {
    document.getElementById('recordingUploadModal').classList.add('hidden');
    document.getElementById('recordingUploadForm').reset();
    document.getElementById('recordingFileInfo').classList.add('hidden');
    document.getElementById('recordingUploadProgress').classList.add('hidden');
}

function clearRecordingFile() {
    document.getElementById('recordingFile').value = '';
    document.getElementById('recordingFileInfo').classList.add('hidden');
}

function uploadRecording() {
    const form = document.getElementById('recordingUploadForm');
    const formData = new FormData(form);
    const fileInput = document.getElementById('recordingFile');

    if (!fileInput.files[0]) {
        showNotification('Please select a recording file to upload.', 'error');
        return;
    }

    // Debug: Log what we're sending
    console.log('Upload URL:', `/meetings/${meeting.id}/attachments`);
    console.log('Meeting ID:', meeting.id);
    console.log('File:', fileInput.files[0]);
    console.log('Form data entries:');
    for (let pair of formData.entries()) {
        console.log(pair[0] + ': ' + pair[1]);
    }

    // Show progress
    document.getElementById('recordingUploadProgress').classList.remove('hidden');
    document.getElementById('recordingUploadBtn').disabled = true;

    // Create XMLHttpRequest for progress tracking
    const xhr = new XMLHttpRequest();

    // Track upload progress
    xhr.upload.addEventListener('progress', function(e) {
        if (e.lengthComputable) {
            const percentComplete = (e.loaded / e.total) * 100;
            document.getElementById('recordingUploadPercent').textContent = Math.round(percentComplete) + '%';
            document.getElementById('recordingUploadBar').style.width = percentComplete + '%';
        }
    });

    xhr.onload = function() {
        document.getElementById('recordingUploadBtn').disabled = false;
        document.getElementById('recordingUploadProgress').classList.add('hidden');

        console.log('Upload response status:', xhr.status);
        console.log('Upload response text:', xhr.responseText);
        console.log('Upload response headers:', xhr.getAllResponseHeaders());

        if (xhr.status === 200) {
            try {
                const response = JSON.parse(xhr.responseText);
                console.log('Parsed response:', response);
                if (response.success) {
                    showNotification('Recording uploaded successfully!', 'success');
                    closeRecordingUploadModal();
                    // Refresh the recordings tab
                    location.reload();
                } else {
                    showNotification(response.message || 'Failed to upload recording.', 'error');
                }
            } catch (e) {
                console.error('Failed to parse response as JSON:', e);
                console.error('Raw response:', xhr.responseText);
                showNotification('Server returned an invalid response. Please check the console for details.', 'error');
            }
        } else if (xhr.status === 422) {
            // Validation error
            try {
                const response = JSON.parse(xhr.responseText);
                const errors = response.errors || {};
                const errorMessages = Object.values(errors).flat();
                showNotification(errorMessages.join(', ') || 'Validation failed.', 'error');
            } catch (e) {
                console.error('Failed to parse validation error response:', xhr.responseText);
                showNotification('Validation failed. Please check your file and try again.', 'error');
            }
        } else if (xhr.status === 413) {
            showNotification('File is too large. Please select a file smaller than the server limit.', 'error');
        } else {
            console.error('Upload failed with status:', xhr.status);
            console.error('Response text:', xhr.responseText);
            showNotification(`Upload failed (${xhr.status}). Please try again.`, 'error');
        }
    };

    xhr.onerror = function() {
        showNotification('Upload failed. Please check your connection and try again.', 'error');
        document.getElementById('recordingUploadBtn').disabled = false;
        document.getElementById('recordingUploadProgress').classList.add('hidden');
    };

    xhr.open('POST', `/meetings/${meeting.id}/attachments`);
    xhr.setRequestHeader('X-CSRF-TOKEN', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
    xhr.send(formData);
}

function downloadRecording(recordingId) {
    window.open(`/meetings/${meeting.id}/attachments/${recordingId}/download`, '_blank');
}

function handleVideoError(videoElement, recordingId) {
    console.error('Video playback error for recording', recordingId, videoElement.error);

    // Hide loading indicator
    hideVideoLoading(recordingId);

    // Show error message to user
    const errorDiv = document.createElement('div');
    errorDiv.className = 'text-white p-4 text-center';
    errorDiv.innerHTML = `
        <p class="mb-2">Unable to play this video.</p>
        <a href="/meetings/${meeting.id}/attachments/${recordingId}/download"
           class="text-blue-400 hover:text-blue-300 underline">
            Download the video file
        </a>
    `;

    // Replace video element with error message
    videoElement.parentNode.replaceChild(errorDiv, videoElement);
}

function showVideoLoading(recordingId) {
    const loadingElement = document.getElementById(`video-loading-${recordingId}`);
    if (loadingElement) {
        loadingElement.style.display = 'flex';
    }
}

function hideVideoLoading(recordingId) {
    const loadingElement = document.getElementById(`video-loading-${recordingId}`);
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }
}

function deleteRecording(recordingId) {
    if (confirm('Are you sure you want to delete this recording? This action cannot be undone.')) {
        fetch(`/meetings/${meeting.id}/attachments/${recordingId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Recording deleted successfully!', 'success');
                document.querySelector(`[data-recording-id="${recordingId}"]`).remove();
            } else {
                showNotification(data.message || 'Failed to delete recording.', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('An error occurred while deleting the recording.', 'error');
        });
    }
}

function shareRecording(recordingId) {
    const shareUrl = `${window.location.origin}/meetings/${meeting.id}/attachments/${recordingId}/download`;

    // Try native sharing first (mobile devices)
    if (navigator.share) {
        navigator.share({
            title: 'Meeting Recording',
            text: `Recording from meeting: ${meeting.title}`,
            url: shareUrl
        }).catch((error) => {
            console.error('Error sharing:', error);
            // Fallback to clipboard if sharing fails
            fallbackToClipboard(shareUrl);
        });
    } else {
        // Fallback to clipboard for desktop browsers
        fallbackToClipboard(shareUrl);
    }
}

function fallbackToClipboard(shareUrl) {
    // Show loading state
    showNotification('Copying link...', 'info');

    // Check if clipboard API is available and secure context
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(shareUrl).then(() => {
            showNotification('Recording link copied to clipboard!', 'success');
        }).catch((error) => {
            console.error('Clipboard write failed:', error);
            showNotification('Clipboard access failed, trying alternative method...', 'info');
            setTimeout(() => tryLegacyClipboard(shareUrl), 1000);
        });
    } else {
        // Try legacy clipboard method
        setTimeout(() => tryLegacyClipboard(shareUrl), 500);
    }
}

function tryLegacyClipboard(shareUrl) {
    try {
        // Create a temporary textarea element
        const textArea = document.createElement('textarea');
        textArea.value = shareUrl;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);

        textArea.focus();
        textArea.select();

        // Try to copy using execCommand
        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);

        if (successful) {
            showNotification('Recording link copied to clipboard!', 'success');
        } else {
            fallbackToPrompt(shareUrl);
        }
    } catch (error) {
        console.error('Legacy clipboard failed:', error);
        fallbackToPrompt(shareUrl);
    }
}

function fallbackToPrompt(shareUrl) {
    // Final fallback: show URL in prompt for manual copying
    const userCopied = prompt('Copy this link to share the recording:', shareUrl);
    if (userCopied !== null) {
        showNotification('Please copy the link from the dialog above', 'info');
    }
}

function showNotification(message, type) {
    // Remove any existing notifications to prevent stacking
    const existingNotifications = document.querySelectorAll('.notification-toast');
    existingNotifications.forEach(notification => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    });

    const notification = document.createElement('div');

    let bgColor;
    let duration = 3000; // Default duration
    switch(type) {
        case 'success':
            bgColor = 'bg-green-500 text-white';
            duration = 2000; // Shorter for success messages
            break;
        case 'info':
            bgColor = 'bg-blue-500 text-white';
            duration = 2500;
            break;
        case 'error':
        default:
            bgColor = 'bg-red-500 text-white';
            duration = 4000; // Longer for error messages
            break;
    }

    notification.className = `notification-toast fixed top-4 right-4 p-4 rounded-md shadow-lg z-50 transition-all duration-300 ${bgColor}`;
    notification.textContent = message;

    // Add close button
    const closeButton = document.createElement('button');
    closeButton.innerHTML = '×';
    closeButton.className = 'ml-2 text-white hover:text-gray-200 font-bold text-lg leading-none';
    closeButton.onclick = () => {
        if (notification.parentNode) {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }
    };

    notification.appendChild(closeButton);
    document.body.appendChild(notification);

    // Auto-dismiss after duration
    setTimeout(() => {
        if (notification.parentNode) {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }
    }, duration);
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/meetings/Views/show.blade.php ENDPATH**/ ?>